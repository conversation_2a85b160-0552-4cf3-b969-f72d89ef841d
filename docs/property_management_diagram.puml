@startuml Property Management System

class Property {
  +int id
  +string name
  +string address
  +string description
  +Contact contact
  +int city_id
  +int area_id
  +float latitude
  +float longitude
  +int[] media_ids
  +int property_type_id
  +datetime created_at
  +datetime updated_at
  +bool is_active
}
class Contact {
  +string full_name
  +string phone
  +string email
  +datetime created_at
  +datetime updated_at
}

class PropertyType {
  +int id
  +string name
  +string description
  +datetime created_at
  +datetime updated_at
}

class Room {
  +int id
  +string name
  +string description
  +float area
  +int property_id
  +int room_type_id
  +int[] media_ids
  +int[] category_ids
  +int[] amenity_ids
  +string status
  +datetime created_at
  +datetime updated_at
}
class RoomPricing {
  +int id
  +int room_id
  +string pricing_type         // "monthly", "daily", etc.
  +decimal base_price
  +decimal deposit
  +map[string]decimal fees
  +string currency
  +datetime created_at
  +datetime updated_at
}
class RoomType {
  +int id
  +string name
  +string description
  +datetime created_at
  +datetime updated_at
}



class Amenity {
  +int id
  +string name
  +string description
  +string icon
  +datetime created_at
  +datetime updated_at
}

class Media {
  +int id
  +string data
  +string description
  +bool is_primary
  +string type
  +datetime created_at
  +datetime updated_at
}




class Category {
  +int id
  +string name
  +string icon
  +string description
  +int parent_id
  +datetime created_at
  +datetime updated_at

}

class Room {
  +int room_id
  +int amenity_id
}


Property "1" -- "many" Room : contains >
Property "many" -- "1" PropertyType : is of >

Room "many" -- "1" RoomType : is of >
Room "1" --> "many" RoomPricing : has

Room "many" -- "many" Amenity 

Room "many" -- "many" Category 

Category "many" -- "0..1" Category : has parent >

Room "1" -- "many" Media : has
Property "1" -- "many" Media : has
Property *-- Contact

  
@enduml
