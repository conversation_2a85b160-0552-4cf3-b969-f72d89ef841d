package connectrpc

import (
	"context"
	categorydto "property_service/internal/dtos/category"
	"property_service/internal/infra/global"
	"property_service/pkg/helper"

	"connectrpc.com/connect"
	propertyv1 "gitlab.com/bds4430521/proto-bds/gen/property/v1"
	"gitlab.com/bds4430521/proto-bds/gen/statusmsg/v1"
	utilsv1 "gitlab.com/bds4430521/proto-bds/gen/utils/v1"
)

// CreateCategory implements propertyv1connect.PropertyServiceHandler.
func (p *propertyServerHandler) CreateCategory(ctx context.Context, req *connect.Request[propertyv1.CreateCategoryRequest]) (res *connect.Response[propertyv1.CreateCategoryResponse], err error) {
	res = connect.NewResponse(&propertyv1.CreateCategoryResponse{
		Status: &statusmsg.StatusMessage{},
	})
	dto := categorydto.CreateCategoryRequest{
		Name:        req.Msg.GetName(),
		Description: req.Msg.GetDescription(),
		Icon:        req.Msg.GetIcon(),
		ParentGuid:  req.Msg.GetParentGuid(),
		Code:        req.Msg.GetCode(),
	}
	if err := global.Validate.Struct(dto); err != nil {
		res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.Msg.Status.Extras = []string{err.Error()}
		return res, nil
	}
	result, err := p.useCases.GetCategoriesUseCase().CreateCategory(ctx, dto)

	if err != nil {
		res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}

	res.Msg.Status.Code = result.Code
	res.Msg.Status.Extras = result.Extras
	return res, nil

}

// DeleteCategory implements propertyv1connect.PropertyServiceHandler.
func (p *propertyServerHandler) DeleteCategory(ctx context.Context, req *connect.Request[propertyv1.DeleteCategoryRequest]) (res *connect.Response[propertyv1.DeleteCategoryResponse], err error) {
	res = connect.NewResponse(&propertyv1.DeleteCategoryResponse{
		Status: &statusmsg.StatusMessage{},
	})
	dto := categorydto.DeleteCategoryRequest{
		Guid: req.Msg.GetGuid(),
	}
	if err := global.Validate.Struct(dto); err != nil {
		res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.Msg.Status.Extras = []string{err.Error()}
		return res, nil
	}
	result, err := p.useCases.GetCategoriesUseCase().DeleteCategory(ctx, dto)

	if err != nil {
		res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	res.Msg.Status.Code = result.Code
	res.Msg.Status.Extras = result.Extras
	return res, nil
}

// FetchCategories implements propertyv1connect.PropertyServiceHandler.
func (p *propertyServerHandler) FetchCategories(context.Context, *connect.Request[propertyv1.FetchCategoriesRequest]) (*connect.Response[propertyv1.FetchCategoriesResponse], error) {
	panic("unimplemented")
}

// SearchAdvanceCategories implements propertyv1connect.PropertyServiceHandler.
func (p *propertyServerHandler) SearchAdvanceCategories(ctx context.Context, req *connect.Request[utilsv1.SearchAdvanceRequest]) (*connect.Response[propertyv1.SearchAdvanceCategoriesResponse], error) {
	res := connect.NewResponse(&propertyv1.SearchAdvanceCategoriesResponse{
		Status: &statusmsg.StatusMessage{},
	})
	query := helper.ConvertFilter(req)

	result, err := p.useCases.GetCategoriesUseCase().SearchAdvance(ctx, query)
	if err != nil {
		res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		return res, err
	}
	res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_SUCCESS
	res.Msg.Total = int64(result.Total)
	res.Msg.Rows = make([]*propertyv1.CategoryModel, len(result.Rows))
	for i, item := range result.Rows {
		res.Msg.Rows[i] = &propertyv1.CategoryModel{
			Guid:        item.Guid,
			Name:        item.Name,
			Description: item.Description,
			Icon:        item.Icon,
			ParentGuid:  item.ParentGuid,
			Code:        item.Code,
		}
	}
	return res, nil
}

// UpdateCategory implements propertyv1connect.PropertyServiceHandler.
func (p *propertyServerHandler) UpdateCategory(ctx context.Context, req *connect.Request[propertyv1.UpdateCategoryRequest]) (res *connect.Response[propertyv1.UpdateCategoryResponse], err error) {
	res = connect.NewResponse(&propertyv1.UpdateCategoryResponse{
		Status: &statusmsg.StatusMessage{},
	})
	dto := categorydto.UpdateCategoryRequest{
		Name:        req.Msg.GetName(),
		Description: req.Msg.GetDescription(),
		Icon:        req.Msg.GetIcon(),
		Guid:        req.Msg.GetGuid(),
		ParentGuid:  req.Msg.GetParentGuid(),
		Code:        req.Msg.GetCode(),
	}
	if err := global.Validate.Struct(dto); err != nil {
		res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.Msg.Status.Extras = []string{err.Error()}
		return res, nil
	}
	result, err := p.useCases.GetCategoriesUseCase().UpdateCategory(ctx, dto)

	if err != nil {
		res.Msg.Status.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	res.Msg.Status.Code = result.Code
	res.Msg.Status.Extras = result.Extras
	return res, nil
}
