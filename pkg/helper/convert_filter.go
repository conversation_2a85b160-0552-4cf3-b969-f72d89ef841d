package helper

import (
	dtos "property_service/internal/dtos/shared"

	"connectrpc.com/connect"
	utilsv1 "gitlab.com/bds4430521/proto-bds/gen/utils/v1"
)

func ConvertFilter(req *connect.Request[utilsv1.SearchAdvanceRequest]) dtos.SearchAdvanceModel {
	query := dtos.SearchAdvanceModel{
		Filters: make(map[string]dtos.FilterModel),
	}

	for _, filter := range req.Msg.GetFilters() {
		query.Filters[filter.GetField()] = dtos.FilterModel{
			Type:   filter.GetType(),
			Filter: filter.GetFilter(),
		}
	}

	for _, sort := range req.Msg.GetSort() {
		query.Sort = append(query.Sort, dtos.SortModelItem{
			ColId: sort.GetColId(),
			Sort:  sort.GetSort(),
		})
	}
	return query
}
