package categorydto

import "gitlab.com/bds4430521/proto-bds/gen/statusmsg/v1"

type CreateCategoryRequest struct {
	Name        string `json:"name" validate:"required"`
	Description string `json:"description" validate:"required"`
	Icon        string `json:"icon" validate:"required"`
	ParentGuid  string `json:"parent_guid"`
	Code        string `json:"code" validate:"required"`
}

type CreateCategoryResponse struct {
	*statusmsg.StatusMessage
}

type UpdateCategoryRequest struct {
	Name        string `json:"name" validate:"required"`
	Description string `json:"Category_id" validate:"required"`
	Icon        string `json:"icon" validate:"required"`
	Guid        string `json:"guid" validate:"required"`
	ParentGuid  string `json:"parent_guid"`
	Code        string `json:"code" validate:"required"`
}
type UpdateCategoryResponse struct {
	*statusmsg.StatusMessage
}

type DeleteCategoryRequest struct {
	Guid string `json:"guid" validate:"required"`
}
type DeleteCategoryResponse struct {
	*statusmsg.StatusMessage
}

type FetchAmenitiesRequest struct {
	Page     int32
	PageSize int32
}

type CategoryModel struct {
	Guid        string `json:"guid"`
	Name        string `json:"name" validate:"required"`
	Description string `json:"Category_id" validate:"required"`
	Icon        string `json:"icon" validate:"required"`
	ParentGuid  string `json:"parent_guid" validate:"required"`
	Code        string `json:"code" validate:"required"`
}

type FetchAmenitiesResponse struct {
	Items      []CategoryModel
	Total      int
	Page       int32
	PageSize   int32
	TotalPages int32
}
