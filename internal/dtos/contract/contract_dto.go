package contractdto

import "gitlab.com/bds4430521/proto-bds/gen/statusmsg/v1"

type CreateContractRequest struct {
	FullName string `json:"full_name" validate:"required"`
	Phone    string `json:"phone" validate:"required"`
	Email    string `json:"email" validate:"required,email"`
}

type CreateContractResponse struct {
	*statusmsg.StatusMessage
}

type UpdateContractRequest struct {
	FullName string `json:"full_name" validate:"required"`
	Phone    string `json:"phone" validate:"required"`
	Email    string `json:"email" validate:"required,email"`
	Guid     string `json:"guid" validate:"required"`
}

type UpdateContractResponse struct {
	*statusmsg.StatusMessage
}

type DeleteContractRequest struct {
	Guid string `json:"guid" validate:"required"`
}

type DeleteContractResponse struct {
	*statusmsg.StatusMessage
}

type FetchContractsRequest struct {
	Page     int32
	PageSize int32
}

type ContractModel struct {
	Guid     string `json:"guid"`
	FullName string `json:"full_name" validate:"required"`
	Phone    string `json:"phone" validate:"required"`
	Email    string `json:"email" validate:"required,email"`
}

type FetchContractsResponse struct {
	Items      []ContractModel
	Total      int
	Page       int32
	PageSize   int32
	TotalPages int32
}
