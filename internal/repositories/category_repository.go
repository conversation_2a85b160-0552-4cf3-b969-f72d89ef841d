package repositories

import (
	"context"
	"property_service/internal/entities"
	db_helper "property_service/internal/infra/db/helpers"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type CategoryRepositoryInterface interface {
	GetBaseRepo() Repository[entities.Category]
	GetByCode(ctx context.Context, code string) (*entities.Category, error)
}

type categoryRepository struct {
	base       Repository[entities.Category]
	collection *mongo.Collection
}

func NewCategoryRepository(collection *mongo.Collection) CategoryRepositoryInterface {
	return &categoryRepository{
		base:       NewRepository[entities.Category](collection),
		collection: collection,
	}
}

func (r *categoryRepository) GetByCode(ctx context.Context, code string) (*entities.Category, error) {
	var result entities.Category
	filter := bson.M{"code": code}
	filter = db_helper.BuildFilter(ctx, filter)
	err := r.collection.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}
	return &result, nil
}

func (r *categoryRepository) GetBaseRepo() Repository[entities.Category] {
	return r.base
}
