package usercase_categories

import (
	"context"
	categorydto "property_service/internal/dtos/category"
	dtos "property_service/internal/dtos/shared"
	"property_service/internal/entities"
	"property_service/internal/infra"

	"gitlab.com/bds4430521/proto-bds/gen/statusmsg/v1"
)

type crudCategoryUseCase struct {
	Cabin infra.Cabin
}

type CrudCategoryUseCase interface {
	CreateCategory(ctx context.Context, req categorydto.CreateCategoryRequest) (*categorydto.CreateCategoryResponse, error)
	UpdateCategory(ctx context.Context, req categorydto.UpdateCategoryRequest) (*categorydto.UpdateCategoryResponse, error)
	DeleteCategory(ctx context.Context, req categorydto.DeleteCategoryRequest) (*categorydto.DeleteCategoryResponse, error)
	SearchAdvance(ctx context.Context, req dtos.SearchAdvanceModel) (*dtos.SearchAdvanceResponse[categorydto.CategoryModel], error)
}

func NewCrudsCategoryUseCase(cabin infra.Cabin) CrudCategoryUseCase {
	return &crudCategoryUseCase{
		Cabin: cabin,
	}
}

// SearchAdvance implements CrudCategoryUseCase.
func (s *crudCategoryUseCase) SearchAdvance(ctx context.Context, req dtos.SearchAdvanceModel) (*dtos.SearchAdvanceResponse[categorydto.CategoryModel], error) {
	res := &dtos.SearchAdvanceResponse[categorydto.CategoryModel]{}
	paging, err := s.Cabin.GetUnitOfWork().GetCategoryRepository().GetBaseRepo().SearchAdvance(ctx, req)
	if err != nil {
		return nil, err
	}
	if paging == nil {
		return nil, ErrInvalidRequest
	}

	res.Rows = make([]categorydto.CategoryModel, len(paging.Rows))
	for i, item := range paging.Rows {
		res.Rows[i] = categorydto.CategoryModel{
			Guid:        item.Guid,
			Name:        item.Name,
			Description: item.Description,
			Icon:        item.Icon,
			Code:        item.Code,
			ParentGuid:  item.ParentID,
		}
	}
	res.Total = paging.Total
	return res, nil
}
func (s *crudCategoryUseCase) CreateCategory(ctx context.Context, req categorydto.CreateCategoryRequest) (*categorydto.CreateCategoryResponse, error) {
	res := &categorydto.CreateCategoryResponse{
		StatusMessage: &statusmsg.StatusMessage{},
	}

	exist, err := s.Cabin.GetUnitOfWork().GetCategoryRepository().GetByCode(ctx, req.Code)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	if exist != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.StatusMessage.Extras = []string{ErrCategoryExist.Error()}
		return res, nil
	}

	if req.ParentGuid != "" {
		parent, err := s.Cabin.GetUnitOfWork().GetCategoryRepository().GetBaseRepo().GetByGuid(ctx, req.ParentGuid)
		if err != nil {
			res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
			return res, err
		}
		if parent == nil {
			res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
			res.StatusMessage.Extras = []string{ErrCategoryParentNotFound.Error()}
			return res, nil
		}
	}

	CategoryEntity := &entities.Category{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		BaseEntity:  entities.NewBaseEntity(ctx),
		Code:        req.Code,
		ParentID:    req.ParentGuid,
	}
	_, err = s.Cabin.GetUnitOfWork().GetCategoryRepository().GetBaseRepo().Create(ctx, CategoryEntity)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_UNSPECIFIED
		return res, err
	}
	res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_SUCCESS
	return res, nil
}

func (s *crudCategoryUseCase) UpdateCategory(ctx context.Context, req categorydto.UpdateCategoryRequest) (*categorydto.UpdateCategoryResponse, error) {
	res := &categorydto.UpdateCategoryResponse{
		StatusMessage: &statusmsg.StatusMessage{},
	}

	exist, err := s.Cabin.GetUnitOfWork().GetCategoryRepository().GetBaseRepo().GetByGuid(ctx, req.Guid)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_UNSPECIFIED
		return res, err
	}
	if exist == nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.StatusMessage.Extras = []string{ErrCategoryNotFound.Error()}
		return res, nil
	}
	if exist.Code != req.Code {
		existByCode, err := s.Cabin.GetUnitOfWork().GetCategoryRepository().GetByCode(ctx, req.Code)
		if err != nil {
			res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
			return res, err
		}
		if existByCode != nil && existByCode.Guid != req.Guid {
			res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
			res.StatusMessage.Extras = []string{ErrCategoryExist.Error()}
			return res, nil
		}
	}

	if req.ParentGuid != exist.ParentID && req.ParentGuid == "" {

		parent, err := s.Cabin.GetUnitOfWork().GetCategoryRepository().GetBaseRepo().GetByGuid(ctx, req.ParentGuid)
		if err != nil {
			res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
			return res, err
		}
		if parent == nil {
			res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
			res.StatusMessage.Extras = []string{ErrCategoryParentNotFound.Error()}
			return res, nil
		}
	}

	exist.Name = req.Name
	exist.Description = req.Description
	exist.Icon = req.Icon
	exist.Code = req.Code
	exist.ParentID = req.ParentGuid
	_, err = s.Cabin.GetUnitOfWork().GetCategoryRepository().GetBaseRepo().UpdateByGuid(ctx, req.Guid, exist)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_SUCCESS
	return res, nil

}

func (s *crudCategoryUseCase) DeleteCategory(ctx context.Context, req categorydto.DeleteCategoryRequest) (*categorydto.DeleteCategoryResponse, error) {
	res := &categorydto.DeleteCategoryResponse{
		StatusMessage: &statusmsg.StatusMessage{},
	}
	err := s.Cabin.GetUnitOfWork().GetCategoryRepository().GetBaseRepo().DeleteByGuid(ctx, req.Guid)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_UNSPECIFIED
		return res, err
	}
	res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_SUCCESS
	return res, nil
}
