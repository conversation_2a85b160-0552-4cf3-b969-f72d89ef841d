package usecases

import (
	"property_service/internal/infra"
	usercase_amenities "property_service/internal/usecases/amenities"
	usercase_categories "property_service/internal/usecases/categories"
)

type UsecaseManager interface {
	GetAmenitiesUseCase() usercase_amenities.AmenityUseCase
	GetCategoriesUseCase() usercase_categories.CategoryUseCase
}

type usecaseManager struct {
	amenitiyUseCases usercase_amenities.AmenityUseCase
	categoryUseCases usercase_categories.CategoryUseCase
}

func NewUsecaseManager(cabin infra.Cabin) UsecaseManager {
	return &usecaseManager{
		amenitiyUseCases: usercase_amenities.NewAmenityUseCase(cabin),
		categoryUseCases: usercase_categories.NewCategoryUseCase(cabin),
	}
}

func (u *usecaseManager) GetCategoriesUseCase() usercase_categories.CategoryUseCase {
	return u.categoryUseCases
}

func (u *usecaseManager) GetAmenitiesUseCase() usercase_amenities.AmenityUseCase {
	return u.amenitiyUseCases
}
