package usercase_contracts

import (
	"context"
	"math"
	contractdto "property_service/internal/dtos/contract"
	dtos "property_service/internal/dtos/shared"
	"property_service/internal/entities"
	"property_service/internal/infra"

	"gitlab.com/bds4430521/proto-bds/gen/statusmsg/v1"
)

type crudContractUseCase struct {
	Cabin infra.Cabin
}

type CrudContractUseCase interface {
	CreateContract(ctx context.Context, req contractdto.CreateContractRequest) (*contractdto.CreateContractResponse, error)
	UpdateContract(ctx context.Context, req contractdto.UpdateContractRequest) (*contractdto.UpdateContractResponse, error)
	DeleteContract(ctx context.Context, req contractdto.DeleteContractRequest) (*contractdto.DeleteContractResponse, error)
	SearchAdvance(ctx context.Context, req dtos.SearchAdvanceModel) (*dtos.SearchAdvanceResponse[contractdto.ContractModel], error)
}

func NewCrudsContractUseCase(cabin infra.Cabin) CrudContractUseCase {
	return &crudContractUseCase{
		Cabin: cabin,
	}
}

// SearchAdvance implements CrudContractUseCase.
func (s *crudContractUseCase) SearchAdvance(ctx context.Context, req dtos.SearchAdvanceModel) (*dtos.SearchAdvanceResponse[contractdto.ContractModel], error) {
	res := &dtos.SearchAdvanceResponse[contractdto.ContractModel]{}
	paging, err := s.Cabin.GetUnitOfWork().GetContractRepository().GetBaseRepo().SearchAdvance(ctx, req)
	if err != nil {
		return nil, err
	}
	if paging == nil {
		return nil, ErrInvalidRequest
	}

	res.Rows = make([]contractdto.ContractModel, len(paging.Rows))
	for i, item := range paging.Rows {
		res.Rows[i] = contractdto.ContractModel{
			Guid:     item.Guid,
			FullName: item.FullName,
			Phone:    item.Phone,
			Email:    item.Email,
		}
	}
	res.Total = paging.Total
	return res, nil
}

func (s *crudContractUseCase) CreateContract(ctx context.Context, req contractdto.CreateContractRequest) (*contractdto.CreateContractResponse, error) {
	res := &contractdto.CreateContractResponse{
		StatusMessage: &statusmsg.StatusMessage{},
	}

	// Check if email already exists
	existByEmail, err := s.Cabin.GetUnitOfWork().GetContractRepository().GetByEmail(ctx, req.Email)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	if existByEmail != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.StatusMessage.Extras = []string{ErrEmailExist.Error()}
		return res, nil
	}

	// Check if phone already exists
	existByPhone, err := s.Cabin.GetUnitOfWork().GetContractRepository().GetByPhone(ctx, req.Phone)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	if existByPhone != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.StatusMessage.Extras = []string{ErrPhoneExist.Error()}
		return res, nil
	}

	contractEntity := &entities.Contract{
		FullName:   req.FullName,
		Phone:      req.Phone,
		Email:      req.Email,
		BaseEntity: entities.NewBaseEntity(ctx),
	}
	_, err = s.Cabin.GetUnitOfWork().GetContractRepository().GetBaseRepo().Create(ctx, contractEntity)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_UNSPECIFIED
		return res, err
	}
	res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_SUCCESS
	return res, nil
}

func (s *crudContractUseCase) UpdateContract(ctx context.Context, req contractdto.UpdateContractRequest) (*contractdto.UpdateContractResponse, error) {
	res := &contractdto.UpdateContractResponse{
		StatusMessage: &statusmsg.StatusMessage{},
	}

	exist, err := s.Cabin.GetUnitOfWork().GetContractRepository().GetBaseRepo().GetByGuid(ctx, req.Guid)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_UNSPECIFIED
		return res, err
	}
	if exist == nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.StatusMessage.Extras = []string{ErrContractNotFound.Error()}
		return res, nil
	}

	// Check if email already exists (excluding current contract)
	existByEmail, err := s.Cabin.GetUnitOfWork().GetContractRepository().GetByEmail(ctx, req.Email)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	if existByEmail != nil && existByEmail.Guid != req.Guid {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.StatusMessage.Extras = []string{ErrEmailExist.Error()}
		return res, nil
	}

	// Check if phone already exists (excluding current contract)
	existByPhone, err := s.Cabin.GetUnitOfWork().GetContractRepository().GetByPhone(ctx, req.Phone)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	if existByPhone != nil && existByPhone.Guid != req.Guid {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_VALIDATION_FAILED
		res.StatusMessage.Extras = []string{ErrPhoneExist.Error()}
		return res, nil
	}

	exist.FullName = req.FullName
	exist.Phone = req.Phone
	exist.Email = req.Email
	_, err = s.Cabin.GetUnitOfWork().GetContractRepository().GetBaseRepo().UpdateByGuid(ctx, req.Guid, exist)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_INTERNAL_ERROR
		return res, err
	}
	res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_SUCCESS
	return res, nil
}

func (s *crudContractUseCase) DeleteContract(ctx context.Context, req contractdto.DeleteContractRequest) (*contractdto.DeleteContractResponse, error) {
	res := &contractdto.DeleteContractResponse{
		StatusMessage: &statusmsg.StatusMessage{},
	}
	err := s.Cabin.GetUnitOfWork().GetContractRepository().GetBaseRepo().DeleteByGuid(ctx, req.Guid)
	if err != nil {
		res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_UNSPECIFIED
		return res, err
	}
	res.StatusMessage.Code = statusmsg.StatusCode_STATUS_CODE_SUCCESS
	return res, nil
}

func (s *crudContractUseCase) GetContractsPaging(ctx context.Context, req contractdto.FetchContractsRequest) (*contractdto.FetchContractsResponse, error) {
	paging, err := s.Cabin.GetUnitOfWork().GetContractRepository().GetContractsPaging(ctx, "", req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	res := &contractdto.FetchContractsResponse{
		Page:       req.Page,
		PageSize:   req.PageSize,
		Total:      paging.Total,
		TotalPages: int32(math.Ceil(float64(paging.Total) / float64(req.PageSize))),
	}

	items := make([]contractdto.ContractModel, len(paging.Items))
	for i, item := range paging.Items {
		items[i] = contractdto.ContractModel{
			Guid:     item.Guid,
			FullName: item.FullName,
			Phone:    item.Phone,
			Email:    item.Email,
		}
	}
	res.Items = items

	return res, nil
}
