package infra

import (
	"property_service/internal/infra/db"

	infrastructurecore "gitlab.com/bds4430521/building_block/infrastructure/core"
)

type Cabin interface {
	GetInfra() infrastructurecore.IInfra
	GetUnitOfWork() db.UnitOfWork
}
type cabin struct {
	infra      infrastructurecore.IInfra
	unitOfWork db.UnitOfWork
}

func NewCabin(infra infrastructurecore.IInfra, unitOfWork db.UnitOfWork) Cabin {
	cabin := &cabin{
		infra:      infra,
		unitOfWork: unitOfWork,
	}
	return cabin
}

func (c *cabin) GetInfra() infrastructurecore.IInfra {
	return c.infra
}

func (c *cabin) GetUnitOfWork() db.UnitOfWork {
	return c.unitOfWork
}
